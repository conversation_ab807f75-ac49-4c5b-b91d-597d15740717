<?php
/**
 * Dakoii Provincial Government Theme functions and definitions
 *
 * @package Dakoii_Provincial_Government_Theme
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme setup
 */
function nols_espa_theme_setup() {
    // Add default posts and comments RSS feed links to head
    add_theme_support('automatic-feed-links');

    // Let WordPress manage the document title
    add_theme_support('title-tag');

    // Enable support for Post Thumbnails on posts and pages
    add_theme_support('post-thumbnails');

    // Add support for responsive embedded content
    add_theme_support('responsive-embeds');

    // Add support for editor styles
    add_theme_support('editor-styles');

    // Add support for wide alignment
    add_theme_support('align-wide');

    // Add support for custom logo
    add_theme_support('custom-logo', array(
        'height'      => 60,
        'width'       => 60,
        'flex-width'  => true,
        'flex-height' => true,
    ));

    // Add support for custom header
    add_theme_support('custom-header', array(
        'default-color'      => 'CE1126',
        'width'              => 1200,
        'height'             => 400,
        'flex-width'         => true,
        'flex-height'        => true,
        'header-text'        => true,  // Enable header text to ensure full functionality
        'uploads'            => true,  // Enable uploads
        'random-default'     => false,
        'default-text-color' => 'CE1126',
        'wp-head-callback'   => 'nols_espa_header_style',
    ));

    // Remove WordPress native custom background support
    // We'll implement our own comprehensive background system

    // Add support for HTML5 markup
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ));

    // Add support for selective refresh for widgets
    add_theme_support('customize-selective-refresh-widgets');

    // Register navigation menus
    register_nav_menus(array(
        'primary' => esc_html__('Primary Menu', 'dakoii-provincial-government-theme'),
    ));

    // Set content width
    if (!isset($content_width)) {
        $content_width = 800;
    }
}
add_action('after_setup_theme', 'nols_espa_theme_setup');

/**
 * Custom header style callback
 */
function nols_espa_header_style() {
    $header_text_color = get_header_textcolor();

    // If no custom color is set, return early
    if (get_theme_support('custom-header', 'default-text-color') === $header_text_color) {
        return;
    }

    // If we get this far, we have custom styles
    ?>
    <style type="text/css">
    <?php if (!display_header_text()) : ?>
        .site-title,
        .site-description {
            position: absolute;
            clip: rect(1px, 1px, 1px, 1px);
        }
    <?php else : ?>
        .site-title a,
        .site-description {
            color: #<?php echo esc_attr($header_text_color); ?>;
        }
    <?php endif; ?>
    </style>
    <?php
}

/**
 * Register widget areas
 */
function nols_espa_theme_widgets_init() {
    register_sidebar(array(
        'name'          => esc_html__('Primary Sidebar', 'dakoii-provincial-government-theme'),
        'id'            => 'sidebar-1',
        'description'   => esc_html__('Add widgets here to appear in your sidebar.', 'dakoii-provincial-government-theme'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));

    register_sidebar(array(
        'name'          => esc_html__('Footer Widgets', 'dakoii-provincial-government-theme'),
        'id'            => 'footer-widgets',
        'description'   => esc_html__('Add widgets here to appear in your footer.', 'dakoii-provincial-government-theme'),
        'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'nols_espa_theme_widgets_init');

/**
 * Enqueue scripts and styles
 */
function nols_espa_theme_scripts() {
    // Enqueue main stylesheet
    wp_enqueue_style('nols-espa-theme-style', get_stylesheet_uri(), array(), '1.0.0');

    // Enqueue Google Fonts - Quicksand
    wp_enqueue_style('nols-espa-theme-fonts', 'https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap', array(), null);

    // Enqueue theme JavaScript
    wp_enqueue_script('nols-espa-theme-script', get_template_directory_uri() . '/js/theme.js', array(), '1.0.0', true);

    // Enqueue comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'nols_espa_theme_scripts');

/**
 * Enqueue media uploader scripts for customizer
 */
function nols_espa_customizer_scripts() {
    // Check if user has permission to upload files
    if (!current_user_can('upload_files')) {
        return;
    }

    // Enqueue media scripts with all dependencies
    wp_enqueue_media();
    wp_enqueue_script('media-upload');
    wp_enqueue_script('media-views');
    wp_enqueue_script('media-editor');
    wp_enqueue_script('media-audiovideo');

    // Add CSS to ensure upload controls are visible
    wp_add_inline_style('customize-controls', '
        /* Ensure header image upload controls are visible */
        #customize-control-header_image .button,
        #customize-control-header_image .upload-button,
        #customize-control-header_image .add-new-image,
        #customize-control-header_image input[type="button"],
        .customize-control-header_image .button,
        .customize-control-header_image .upload-button {
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            height: auto !important;
            width: auto !important;
            overflow: visible !important;
        }

        /* Ensure the entire header image control is visible */
        #customize-control-header_image,
        .customize-control-header_image {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* Header image control styling */
        #customize-control-header_image {
            /* Uses default WordPress customizer styling */
        }

        /* Force visibility of ALL elements in header image control */
        #customize-control-header_image * {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            height: auto !important;
            width: auto !important;
            overflow: visible !important;
            position: static !important;
        }

        /* Specific targeting for common upload button patterns */
        #customize-control-header_image button,
        #customize-control-header_image input,
        #customize-control-header_image .wp-media-buttons-icon,
        #customize-control-header_image .media-button,
        #customize-control-header_image .media-button-select,
        #customize-control-header_image .change-image,
        #customize-control-header_image .new-image,
        #customize-control-header_image .remove-image {
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            background: lime !important;
            border: 2px solid blue !important;
            padding: 5px !important;
            margin: 2px !important;
        }
    ');

    // Add user capability information for JavaScript
    wp_localize_script('customize-controls', 'nolsEspaCustomizer', array(
        'canUploadFiles' => current_user_can('upload_files'),
        'canEditThemeOptions' => current_user_can('edit_theme_options'),
        'nonce' => wp_create_nonce('nols_espa_customizer_nonce')
    ));

    // Add detailed debugging script
    wp_add_inline_script('customize-controls', '
        (function($) {
            wp.customize.bind("ready", function() {

                // Check if user has upload permissions
                if (typeof nolsEspaCustomizer !== "undefined" && !nolsEspaCustomizer.canUploadFiles) {
                    return; // Exit if user cannot upload files
                }

                // Check header image functionality after a delay
                setTimeout(function() {
                    if (wp.customize.section("header_image")) {
                        // Check for header image control
                        if (wp.customize.control("header_image")) {
                            // Get the actual control element
                            var headerControl = wp.customize.control("header_image");
                            var controlElement = headerControl.container[0];

                            // Look for upload buttons
                            var uploadButtons = $(controlElement).find("button, .button, input[type=button], .upload-button, .add-new-image");
                            uploadButtons.each(function(i, btn) {
                                // Button found - no action needed
                            });

                            // Look for any hidden elements
                            var hiddenElements = $(controlElement).find(":hidden");

                            // Show details about hidden elements and force them visible
                            hiddenElements.each(function(i, elem) {
                                // Force show all hidden elements that might be upload buttons
                                if (elem.tagName === "BUTTON" || elem.tagName === "INPUT" || $(elem).hasClass("button") || $(elem).hasClass("upload")) {
                                    $(elem).show().css({
                                        "display": "inline-block !important",
                                        "visibility": "visible !important",
                                        "opacity": "1 !important",
                                        "height": "auto !important",
                                        "width": "auto !important",
                                        "position": "static !important"
                                    });
                                }
                            });

                            // Force show the control if it exists but is hidden
                            if (controlElement && !$(controlElement).is(":visible")) {
                                $(controlElement).show().css({
                                    "display": "block",
                                    "visibility": "visible",
                                    "opacity": "1"
                                });
                            }

                            // If no upload buttons found, create one manually
                            if (uploadButtons.length === 0) {
                                var manualButton = $("<button>")
                                    .text("Add Header Image")
                                    .addClass("button button-primary manual-upload-button")
                                    .css({
                                        "margin": "5px 5px 5px 0"
                                    })
                                    .click(function(e) {
                                        e.preventDefault();
                                        e.stopPropagation();

                                        // Try to trigger the media library with proper configuration
                                        if (wp.media) {
                                            // Create a properly configured media frame
                                            var frame = wp.media({
                                                title: "Select Header Image",
                                                button: {
                                                    text: "Use as Header Image"
                                                },
                                                multiple: false,
                                                library: {
                                                    type: "image"
                                                },
                                                uploader: true,
                                                allowLocalEdits: true
                                            });

                                            // Set up frame event handlers
                                            frame.on("ready", function() {
                                                // Frame ready
                                            });

                                            frame.on("content:create:browse", function() {
                                                // Content loading
                                            });

                                            frame.on("content:render:browse", function() {
                                                // Content rendered
                                            });

                                            frame.on("content:activate:browse", function() {
                                                // Browse activated
                                            });

                                            // Prevent auto-close and handle selection properly
                                            frame.on("select", function() {
                                                try {
                                                    var attachment = frame.state().get("selection").first().toJSON();

                                                    // Set the header image using WordPress customizer API
                                                    if (wp.customize.control("header_image") && wp.customize.control("header_image").setting) {
                                                        wp.customize.control("header_image").setting.set(attachment.url);

                                                        // Manually close the frame after successful selection
                                                        setTimeout(function() {
                                                            frame.close();
                                                        }, 100);
                                                    }
                                                } catch (error) {
                                                    // Error handling
                                                }
                                            });

                                            // Add error handling for frame close
                                            frame.on("close", function() {
                                                // Frame closed
                                            });

                                            // Open the frame with timeout fallback
                                            try {
                                                frame.open();

                                                // Add timeout to detect if media library fails to load
                                                setTimeout(function() {
                                                    var mediaModal = $(".media-modal");
                                                    var mediaContent = $(".media-frame-content");

                                                    if (mediaModal.length > 0 && mediaContent.length === 0) {
                                                        frame.close();

                                                        // Try alternative method using wp.media.editor
                                                        if (wp.media.editor) {
                                                            wp.media.editor.send.attachment = function(props, attachment) {
                                                                if (wp.customize.control("header_image") && wp.customize.control("header_image").setting) {
                                                                    wp.customize.control("header_image").setting.set(attachment.url);
                                                                }
                                                            };
                                                            wp.media.editor.open("header-image-upload");
                                                        }
                                                    }
                                                }, 3000);

                                            } catch (error) {
                                                // Try the alternative method immediately if main method fails
                                                if (wp.media.editor) {
                                                    wp.media.editor.send.attachment = function(props, attachment) {
                                                        if (wp.customize.control("header_image") && wp.customize.control("header_image").setting) {
                                                            wp.customize.control("header_image").setting.set(attachment.url);
                                                        }
                                                    };
                                                    wp.media.editor.open("header-image-upload");
                                                } else {
                                                    alert("Media library not available. Please refresh the page and try again.");
                                                }
                                            }
                                        } else {
                                            alert("Media library not available. Please refresh the page and try again.");
                                        }
                                    });

                                // Also add a backup button that uses WordPress native method
                                var backupButton = $("<button>")
                                    .text("Alternative Upload")
                                    .addClass("button button-secondary backup-upload-button")
                                    .css({
                                        "margin": "5px 5px 5px 0"
                                    })
                                    .click(function(e) {
                                        e.preventDefault();
                                        e.stopPropagation();

                                        // Try using wp.media.editor as a more reliable alternative
                                        try {
                                            if (wp.media.editor) {
                                                // Set up the callback for when an image is selected
                                                wp.media.editor.send.attachment = function(props, attachment) {
                                                    if (wp.customize.control("header_image") && wp.customize.control("header_image").setting) {
                                                        wp.customize.control("header_image").setting.set(attachment.url);
                                                    }
                                                };

                                                // Open the media editor
                                                wp.media.editor.open("header-image-backup");
                                            } else {
                                                // Final fallback: direct file input
                                                var fileInput = $("<input>")
                                                    .attr("type", "file")
                                                    .attr("accept", "image/*")
                                                    .css("display", "none")
                                                    .change(function() {
                                                        var file = this.files[0];
                                                        if (file) {
                                                            alert("File selected: " + file.name + ". Please upload this file to your WordPress media library first, then return to the customizer to select it.");
                                                        }
                                                    });

                                                $("body").append(fileInput);
                                                fileInput.click();
                                            }
                                        } catch (error) {
                                            alert("Please try refreshing the page or contact support.");
                                        }
                                    });

                                // Add a third button that uses native customizer approach
                                var nativeButton = $("<button>")
                                    .text("Native Upload")
                                    .addClass("button button-secondary native-upload-button")
                                    .css({
                                        "margin": "5px 5px 5px 0"
                                    })
                                    .click(function(e) {
                                        e.preventDefault();
                                        e.stopPropagation();

                                        // Try to use the native customizer image control
                                        try {
                                            var headerImageControl = wp.customize.control("header_image");
                                            if (headerImageControl && headerImageControl.openFrame) {
                                                headerImageControl.openFrame();
                                            } else if (headerImageControl && headerImageControl.container) {
                                                // Try to find and click the native upload button
                                                var nativeUploadBtn = headerImageControl.container.find(".upload, .new, .add");
                                                if (nativeUploadBtn.length > 0) {
                                                    nativeUploadBtn.first().click();
                                                } else {
                                                    if (wp.media.editor) {
                                                        wp.media.editor.send.attachment = function(props, attachment) {
                                                            headerImageControl.setting.set(attachment.url);
                                                        };
                                                        wp.media.editor.open("native-header-upload");
                                                    }
                                                }
                                            }
                                        } catch (error) {
                                            // Error handling
                                        }
                                    });

                                $(controlElement).append(manualButton);
                                $(controlElement).append(backupButton);
                                $(controlElement).append(nativeButton);
                            }
                        }
                    }
                }, 2000);
            });
        })(jQuery);
    ');
}
add_action('customize_controls_enqueue_scripts', 'nols_espa_customizer_scripts');

/**
 * Ensure proper capabilities for customizer
 */
function nols_espa_ensure_customizer_capabilities() {
    // Ensure the current user has upload_files capability
    if (is_admin() && current_user_can('edit_theme_options') && !current_user_can('upload_files')) {
        // Get the current user
        $user = wp_get_current_user();
        if ($user && !empty($user->roles)) {
            // Add upload_files capability to the user's role
            $role = get_role($user->roles[0]);
            if ($role) {
                $role->add_cap('upload_files');
            }
        }
    }
}
add_action('init', 'nols_espa_ensure_customizer_capabilities');

/**
 * Allow file uploads in customizer context
 */
function nols_espa_allow_customizer_uploads($caps, $cap, $user_id, $args) {
    // Allow upload_files capability in customizer context
    if ($cap === 'upload_files' && is_customize_preview()) {
        return array();
    }
    return $caps;
}
add_filter('map_meta_cap', 'nols_espa_allow_customizer_uploads', 10, 4);

/**
 * Ensure media uploads are enabled
 */
function nols_espa_enable_media_uploads() {
    // Force enable file uploads if in customizer
    if (isset($_GET['customize_theme']) || is_customize_preview()) {
        if (!ini_get('file_uploads')) {
            @ini_set('file_uploads', '1');
        }
        if (!ini_get('upload_max_filesize')) {
            @ini_set('upload_max_filesize', '10M');
        }
    }
}
add_action('init', 'nols_espa_enable_media_uploads');

/**
 * Fix customizer media upload permissions
 */
function nols_espa_fix_customizer_media_permissions() {
    // Check if we're in customizer context
    if (isset($_GET['customize_theme']) || (defined('DOING_AJAX') && DOING_AJAX && isset($_POST['action']) && strpos($_POST['action'], 'customize') !== false)) {
        // Temporarily grant upload permissions for customizer
        add_filter('user_has_cap', function($caps, $cap, $name, $user) {
            if (in_array('upload_files', $cap) && current_user_can('edit_theme_options')) {
                $caps['upload_files'] = true;
            }
            return $caps;
        }, 10, 4);
    }
}
add_action('wp_loaded', 'nols_espa_fix_customizer_media_permissions');

/**
 * Debug function to check user capabilities (temporary)
 */
function nols_espa_debug_user_caps() {
    if (isset($_GET['customize_theme']) && current_user_can('edit_theme_options')) {
        error_log('NOLS ESPA DEBUG: User ID: ' . get_current_user_id());
        error_log('NOLS ESPA DEBUG: User roles: ' . implode(', ', wp_get_current_user()->roles));
        error_log('NOLS ESPA DEBUG: Can upload files: ' . (current_user_can('upload_files') ? 'YES' : 'NO'));
        error_log('NOLS ESPA DEBUG: Can edit theme options: ' . (current_user_can('edit_theme_options') ? 'YES' : 'NO'));
        error_log('NOLS ESPA DEBUG: File uploads enabled: ' . (ini_get('file_uploads') ? 'YES' : 'NO'));
    }
}
add_action('init', 'nols_espa_debug_user_caps');

/**
 * Override media upload capability check for customizer
 */
function nols_espa_override_upload_capability($caps, $cap) {
    // If checking upload_files capability and user can edit theme options
    if ($cap === 'upload_files' && current_user_can('edit_theme_options')) {
        // Check if we're in customizer context
        if (isset($_GET['customize_theme']) ||
            (defined('DOING_AJAX') && DOING_AJAX) ||
            is_customize_preview()) {
            return array('edit_theme_options');
        }
    }
    return $caps;
}
add_filter('map_meta_cap', 'nols_espa_override_upload_capability', 1, 2);

/**
 * Custom excerpt length
 */
function nols_espa_theme_excerpt_length($length) {
    return 30;
}
add_filter('excerpt_length', 'nols_espa_theme_excerpt_length');

/**
 * Custom excerpt more
 */
function nols_espa_theme_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'nols_espa_theme_excerpt_more');

/**
 * Add custom classes to body
 */
function nols_espa_theme_body_classes($classes) {
    // Add class for cultural theme
    $classes[] = 'cultural-theme';
    
    // Add class for PNG colors
    $classes[] = 'png-colors';
    
    return $classes;
}
add_filter('body_class', 'nols_espa_theme_body_classes');

/**
 * Customize comment form
 */
function nols_espa_theme_comment_form_defaults($defaults) {
    $defaults['comment_notes_before'] = '<p class="comment-notes">' . esc_html__('Your email address will not be published. Required fields are marked *', 'dakoii-provincial-government-theme') . '</p>';
    $defaults['comment_notes_after'] = '';
    $defaults['title_reply'] = esc_html__('Leave a Comment', 'dakoii-provincial-government-theme');
    $defaults['title_reply_to'] = esc_html__('Leave a Reply to %s', 'dakoii-provincial-government-theme');
    $defaults['cancel_reply_link'] = esc_html__('Cancel Reply', 'dakoii-provincial-government-theme');
    $defaults['label_submit'] = esc_html__('Post Comment', 'dakoii-provincial-government-theme');
    
    return $defaults;
}
add_filter('comment_form_defaults', 'nols_espa_theme_comment_form_defaults');

/**
 * Add cultural pattern widget
 */
class Nols_ESPA_Cultural_Pattern_Widget extends WP_Widget {
    
    public function __construct() {
        parent::__construct(
            'nols_espa_cultural_pattern',
            esc_html__('Cultural Pattern', 'dakoii-provincial-government-theme'),
            array('description' => esc_html__('Display a cultural pattern design element.', 'dakoii-provincial-government-theme'))
        );
    }
    
    public function widget($args, $instance) {
        echo $args['before_widget'];
        
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }
        
        echo '<div class="cultural-pattern"></div>';
        
        echo $args['after_widget'];
    }
    
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : esc_html__('Cultural Pattern', 'dakoii-provincial-government-theme');
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php esc_attr_e('Title:', 'dakoii-provincial-government-theme'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <?php
    }
    
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        return $instance;
    }
}

/**
 * Register cultural pattern widget
 */
function nols_espa_theme_register_widgets() {
    register_widget('Nols_ESPA_Cultural_Pattern_Widget');
}
add_action('widgets_init', 'nols_espa_theme_register_widgets');

/**
 * Get available color combinations
 */
function nols_espa_get_color_combinations() {
    return array(
        'traditional' => array(
            'label' => esc_html__('Traditional PNG Flag', 'dakoii-provincial-government-theme'),
            'colors' => array(
                'red' => '#DC143C',
                'green' => '#000000',
                'yellow' => '#FFD700',
            ),
        ),
        'sepik_heritage' => array(
            'label' => esc_html__('Sepik River Heritage', 'dakoii-provincial-government-theme'),
            'colors' => array(
                'red' => '#B8860B',
                'green' => '#228B22',
                'yellow' => '#DAA520',
            ),
        ),
        'cultural_celebration' => array(
            'label' => esc_html__('Cultural Celebration', 'dakoii-provincial-government-theme'),
            'colors' => array(
                'red' => '#DC143C',
                'green' => '#008B8B',
                'yellow' => '#FF8C00',
            ),
        ),
    );
}

/**
 * Color manipulation helper functions
 */
function nols_espa_hex_to_rgb($hex) {
    $hex = ltrim($hex, '#');
    return array(
        'r' => hexdec(substr($hex, 0, 2)),
        'g' => hexdec(substr($hex, 2, 2)),
        'b' => hexdec(substr($hex, 4, 2)),
    );
}

function nols_espa_rgb_to_hex($r, $g, $b) {
    return sprintf('#%02x%02x%02x', $r, $g, $b);
}

function nols_espa_darken_color($hex, $percent) {
    $rgb = nols_espa_hex_to_rgb($hex);
    $factor = (100 - $percent) / 100;

    $r = max(0, min(255, round($rgb['r'] * $factor)));
    $g = max(0, min(255, round($rgb['g'] * $factor)));
    $b = max(0, min(255, round($rgb['b'] * $factor)));

    return nols_espa_rgb_to_hex($r, $g, $b);
}

function nols_espa_lighten_color($hex, $percent) {
    $rgb = nols_espa_hex_to_rgb($hex);
    $factor = $percent / 100;

    $r = max(0, min(255, round($rgb['r'] + (255 - $rgb['r']) * $factor)));
    $g = max(0, min(255, round($rgb['g'] + (255 - $rgb['g']) * $factor)));
    $b = max(0, min(255, round($rgb['b'] + (255 - $rgb['b']) * $factor)));

    return nols_espa_rgb_to_hex($r, $g, $b);
}

/**
 * Special handling for black color variations
 */
function nols_espa_get_black_variations($base_color) {
    $rgb = nols_espa_hex_to_rgb($base_color);

    // If it's very dark (close to black), create variations differently
    if ($rgb['r'] < 30 && $rgb['g'] < 30 && $rgb['b'] < 30) {
        return array(
            'dark' => '#000000',     // Pure black for dark variant
            'light' => '#333333',    // Dark gray for light variant
        );
    }

    // For other colors, use normal calculation
    return array(
        'dark' => nols_espa_darken_color($base_color, 25),
        'light' => nols_espa_lighten_color($base_color, 30),
    );
}

/**
 * Get current color values with derived colors
 */
function nols_espa_get_current_colors() {
    $combination = get_theme_mod('color_combination', 'traditional');
    $combinations = nols_espa_get_color_combinations();

    // Get base colors
    if ($combination === 'custom') {
        $red = get_theme_mod('png_red_color', '#CE1126');
        $green = get_theme_mod('png_green_color', '#006A4E');
        $yellow = get_theme_mod('png_yellow_color', '#FFD700');
    } else {
        $colors = isset($combinations[$combination]) ? $combinations[$combination]['colors'] : $combinations['traditional']['colors'];
        $red = $colors['red'];
        $green = $colors['green'];
        $yellow = $colors['yellow'];
    }

    // Calculate derived colors with special handling for black
    $green_variations = nols_espa_get_black_variations($green);

    // Calculate text colors that work well with the selected theme
    $text_primary = ($green === '#000000') ? '#333333' : nols_espa_darken_color($green, 80);
    $text_secondary = ($green === '#000000') ? '#666666' : nols_espa_darken_color($green, 60);
    $text_muted = ($green === '#000000') ? '#999999' : nols_espa_darken_color($green, 40);

    return array(
        'png-red' => $red,
        'png-green' => $green,
        'png-yellow' => $yellow,
        'dark-green' => $green_variations['dark'],
        'light-green' => $green_variations['light'],
        'cream' => '#FFF8DC',
        'dark-brown' => '#8B4513',
        'official-blue' => '#1e3a8a',
        'light-gray' => '#f8fafc',
        'medium-gray' => '#64748b',
        'text-primary' => $text_primary,
        'text-secondary' => $text_secondary,
        'text-muted' => $text_muted,
        'border-light' => '#eeeeee',
        'border-medium' => '#cccccc',
        'white' => '#ffffff',
        'black' => '#000000',
        // Pattern colors for SVG backgrounds
        'pattern-yellow-light' => nols_espa_lighten_color($yellow, 20),
        'pattern-green-light' => nols_espa_lighten_color($green, 20),
    );
}

/**
 * Add theme customizer options
 */
function nols_espa_theme_customize_register($wp_customize) {
    // Add cultural colors section
    $wp_customize->add_section('nols_espa_cultural_colors', array(
        'title'    => esc_html__('Cultural Colors', 'dakoii-provincial-government-theme'),
        'priority' => 30,
        'description' => esc_html__('Choose from predefined color combinations or customize individual colors.', 'dakoii-provincial-government-theme'),
    ));

    // Color combination selector
    $wp_customize->add_setting('color_combination', array(
        'default'           => 'traditional',
        'sanitize_callback' => 'nols_espa_sanitize_color_combination',
    ));

    $combinations = nols_espa_get_color_combinations();
    $combination_choices = array('custom' => esc_html__('Custom Colors', 'dakoii-provincial-government-theme'));
    foreach ($combinations as $key => $combination) {
        $combination_choices[$key] = $combination['label'];
    }

    $wp_customize->add_control('color_combination', array(
        'label'    => esc_html__('Color Combination', 'dakoii-provincial-government-theme'),
        'section'  => 'nols_espa_cultural_colors',
        'settings' => 'color_combination',
        'type'     => 'select',
        'choices'  => $combination_choices,
        'priority' => 10,
    ));

    // PNG Red color
    $wp_customize->add_setting('png_red_color', array(
        'default'           => '#DC143C',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'png_red_color', array(
        'label'    => esc_html__('PNG Red Color', 'dakoii-provincial-government-theme'),
        'section'  => 'nols_espa_cultural_colors',
        'settings' => 'png_red_color',
        'priority' => 20,
    )));

    // PNG Black color (was green)
    $wp_customize->add_setting('png_green_color', array(
        'default'           => '#000000',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'png_green_color', array(
        'label'    => esc_html__('PNG Black Color', 'dakoii-provincial-government-theme'),
        'section'  => 'nols_espa_cultural_colors',
        'settings' => 'png_green_color',
        'priority' => 30,
    )));

    // PNG Gold color (was yellow)
    $wp_customize->add_setting('png_yellow_color', array(
        'default'           => '#FFD700',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'png_yellow_color', array(
        'label'    => esc_html__('PNG Gold Color', 'dakoii-provincial-government-theme'),
        'section'  => 'nols_espa_cultural_colors',
        'settings' => 'png_yellow_color',
        'priority' => 40,
    )));





    // Add custom background image section and controls
    $wp_customize->add_section('background_image', array(
        'title' => esc_html__('Background Image', 'dakoii-provincial-government-theme'),
        'priority' => 80,
        'description' => esc_html__('Upload a background image for your website. If no image is selected, a cultural gradient will be displayed.', 'dakoii-provincial-government-theme'),
    ));

    // Background image setting
    $wp_customize->add_setting('background_image', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
        'transport' => 'refresh',
    ));

    // Background image upload control using Media Control
    $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, 'background_image', array(
        'label' => esc_html__('Upload Background Image', 'dakoii-provincial-government-theme'),
        'section' => 'background_image',
        'settings' => 'background_image',
        'mime_type' => 'image',
    )));

    // Background color setting (fallback/overlay)
    $wp_customize->add_setting('background_color', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'background_color', array(
        'label' => esc_html__('Background Color (Optional)', 'dakoii-provincial-government-theme'),
        'section' => 'background_image',
        'settings' => 'background_color',
        'description' => esc_html__('Optional color that shows behind the image or as fallback.', 'dakoii-provincial-government-theme'),
    )));

    // Background repeat setting
    $wp_customize->add_setting('background_repeat', array(
        'default' => 'no-repeat',
        'sanitize_callback' => 'nols_espa_sanitize_background_repeat',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('background_repeat', array(
        'label' => esc_html__('Background Repeat', 'dakoii-provincial-government-theme'),
        'section' => 'background_image',
        'settings' => 'background_repeat',
        'type' => 'select',
        'choices' => array(
            'no-repeat' => esc_html__('No Repeat', 'dakoii-provincial-government-theme'),
            'repeat' => esc_html__('Tile', 'dakoii-provincial-government-theme'),
            'repeat-x' => esc_html__('Tile Horizontally', 'dakoii-provincial-government-theme'),
            'repeat-y' => esc_html__('Tile Vertically', 'dakoii-provincial-government-theme'),
        ),
    ));

    // Background position setting
    $wp_customize->add_setting('background_position', array(
        'default' => 'center center',
        'sanitize_callback' => 'nols_espa_sanitize_background_position',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('background_position', array(
        'label' => esc_html__('Background Position', 'dakoii-provincial-government-theme'),
        'section' => 'background_image',
        'settings' => 'background_position',
        'type' => 'select',
        'choices' => array(
            'left top' => esc_html__('Left Top', 'dakoii-provincial-government-theme'),
            'left center' => esc_html__('Left Center', 'dakoii-provincial-government-theme'),
            'left bottom' => esc_html__('Left Bottom', 'dakoii-provincial-government-theme'),
            'center top' => esc_html__('Center Top', 'dakoii-provincial-government-theme'),
            'center center' => esc_html__('Center Center', 'dakoii-provincial-government-theme'),
            'center bottom' => esc_html__('Center Bottom', 'dakoii-provincial-government-theme'),
            'right top' => esc_html__('Right Top', 'dakoii-provincial-government-theme'),
            'right center' => esc_html__('Right Center', 'dakoii-provincial-government-theme'),
            'right bottom' => esc_html__('Right Bottom', 'dakoii-provincial-government-theme'),
        ),
    ));

    // Background size setting
    $wp_customize->add_setting('background_size', array(
        'default' => 'cover',
        'sanitize_callback' => 'nols_espa_sanitize_background_size',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('background_size', array(
        'label' => esc_html__('Background Size', 'dakoii-provincial-government-theme'),
        'section' => 'background_image',
        'settings' => 'background_size',
        'type' => 'select',
        'choices' => array(
            'auto' => esc_html__('Original Size', 'dakoii-provincial-government-theme'),
            'contain' => esc_html__('Fit to Screen', 'dakoii-provincial-government-theme'),
            'cover' => esc_html__('Fill Screen', 'dakoii-provincial-government-theme'),
        ),
    ));

    // Background attachment setting
    $wp_customize->add_setting('background_attachment', array(
        'default' => 'fixed',
        'sanitize_callback' => 'nols_espa_sanitize_background_attachment',
        'transport' => 'refresh',
    ));

    $wp_customize->add_control('background_attachment', array(
        'label' => esc_html__('Background Attachment', 'dakoii-provincial-government-theme'),
        'section' => 'background_image',
        'settings' => 'background_attachment',
        'type' => 'select',
        'choices' => array(
            'scroll' => esc_html__('Scroll with Page', 'dakoii-provincial-government-theme'),
            'fixed' => esc_html__('Fixed (Parallax Effect)', 'dakoii-provincial-government-theme'),
        ),
    ));
}
add_action('customize_register', 'nols_espa_theme_customize_register');

/**
 * Clear only legacy custom header image data on theme activation
 */
function nols_espa_clear_legacy_header_data() {
    // Only clear legacy custom header image data from previous implementation
    remove_theme_mod('custom_header_image');

    // Clear any other legacy data that might conflict
    remove_theme_mod('header_image_url');
    remove_theme_mod('header_image_width');
    remove_theme_mod('header_image_height');
}
add_action('after_switch_theme', 'nols_espa_clear_legacy_header_data');


/**
 * Enqueue scripts for customizer preview
 */
function nols_espa_customize_preview_scripts() {
    wp_enqueue_script('customize-preview');

    // Add custom preview script for better customizer functionality
    wp_add_inline_script('customize-preview', '
        (function($) {
            $(document).ready(function() {
                // Preview scripts loaded - ready for customizer functionality
            });
        })(jQuery);
    ');
}
add_action('customize_preview_init', 'nols_espa_customize_preview_scripts');



/**
 * Sanitize color combination choice
 */
function nols_espa_sanitize_color_combination($input) {
    $valid_combinations = array_keys(nols_espa_get_color_combinations());
    $valid_combinations[] = 'custom';

    return in_array($input, $valid_combinations) ? $input : 'traditional';
}

/**
 * Sanitize background repeat choice
 */
function nols_espa_sanitize_background_repeat($input) {
    $valid_repeats = array('no-repeat', 'repeat', 'repeat-x', 'repeat-y');
    return in_array($input, $valid_repeats) ? $input : 'no-repeat';
}

/**
 * Sanitize background position choice
 */
function nols_espa_sanitize_background_position($input) {
    $valid_positions = array(
        'left top', 'left center', 'left bottom',
        'center top', 'center center', 'center bottom',
        'right top', 'right center', 'right bottom'
    );
    return in_array($input, $valid_positions) ? $input : 'center center';
}

/**
 * Sanitize background size choice
 */
function nols_espa_sanitize_background_size($input) {
    $valid_sizes = array('auto', 'contain', 'cover');
    return in_array($input, $valid_sizes) ? $input : 'cover';
}

/**
 * Sanitize background attachment choice
 */
function nols_espa_sanitize_background_attachment($input) {
    $valid_attachments = array('scroll', 'fixed');
    return in_array($input, $valid_attachments) ? $input : 'fixed';
}

/**
 * Output dynamic CSS for color combinations
 */
function nols_espa_output_dynamic_colors() {
    $colors = nols_espa_get_current_colors();

    echo '<style id="nols-espa-dynamic-colors">';
    echo ':root {';
    foreach ($colors as $property => $value) {
        echo '--' . esc_attr($property) . ': ' . esc_attr($value) . ';';
    }
    echo '}';
    echo '</style>';
}
add_action('wp_head', 'nols_espa_output_dynamic_colors', 100);

/**
 * Output dynamic CSS for customizer preview
 */
function nols_espa_output_customizer_colors() {
    if (is_customize_preview()) {
        $colors = nols_espa_get_current_colors();

        echo '<style id="nols-espa-customizer-colors">';
        echo ':root {';
        foreach ($colors as $property => $value) {
            echo '--' . esc_attr($property) . ': ' . esc_attr($value) . ';';
        }
        echo '}';
        echo '</style>';
    }
}
add_action('wp_head', 'nols_espa_output_customizer_colors', 101);

/**
 * Output comprehensive background styles
 */
function nols_espa_output_background_styles() {
    $bg_image = get_theme_mod('background_image');
    $bg_color = get_theme_mod('background_color');
    $bg_repeat = get_theme_mod('background_repeat', 'no-repeat');
    $bg_position = get_theme_mod('background_position', 'center center');
    $bg_size = get_theme_mod('background_size', 'cover');
    $bg_attachment = get_theme_mod('background_attachment', 'fixed');
    $colors = nols_espa_get_current_colors();

    // Preload background image for better performance
    if ($bg_image) {
        echo '<link rel="preload" as="image" href="' . esc_url($bg_image) . '">';
    }

    echo '<style id="nols-espa-background-styles">';
    
    if ($bg_image) {
        // Background image is set - output comprehensive CSS
        echo 'body {';
        echo 'background-image: url(' . esc_url($bg_image) . ');';
        echo 'background-repeat: ' . esc_attr($bg_repeat) . ';';
        echo 'background-position: ' . esc_attr($bg_position) . ';';
        echo 'background-size: ' . esc_attr($bg_size) . ';';
        echo 'background-attachment: ' . esc_attr($bg_attachment) . ';';
        
        // Add background color if specified
        if ($bg_color) {
            echo 'background-color: ' . esc_attr($bg_color) . ';';
        }
        
        echo '}';
        
        // Mobile responsive optimizations
        echo '@media (max-width: 768px) {';
        echo 'body {';
        // Force scroll attachment on mobile for better performance
        echo 'background-attachment: scroll !important;';
        // Use contain sizing on mobile to prevent performance issues
        if ($bg_size === 'cover') {
            echo 'background-size: contain !important;';
        }
        echo '}';
        echo '}';
        
    } else {
        // No background image - use cultural gradient fallback
        echo 'body {';
        echo 'background: linear-gradient(135deg, ' . esc_attr($colors['png-green']) . ' 0%, ' . esc_attr($colors['dark-green']) . ' 100%);';
        
        // Add background color if specified
        if ($bg_color) {
            echo 'background-color: ' . esc_attr($bg_color) . ';';
        }
        
        echo '}';
    }
    
    echo '</style>';
}
add_action('wp_head', 'nols_espa_output_background_styles', 5);
